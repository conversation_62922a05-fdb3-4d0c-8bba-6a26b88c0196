<template>
  <view class="container">
    <div class="main">
      <view class="section">
        <view class="section-title">业务类型</view>
        <view class="business-container">
          <view
            v-for="(item, index) in business"
            :key="index"
            :class="[
              'business-item',
              { active: selectedBusiness === item.value },
            ]"
            @click="selectBusiness(item.value)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">气瓶规格</view>
        <view class="specs-container">
          <view
            v-for="(item, index) in (specs || [])"
            :key="index"
            :class="['spec-item', { active: (item.quantity || 0) > 0 }]"
          >
            <image
              :src="item.image"
              mode="aspectFit"
              class="spec-image"
            ></image>
            <view class="spec-info">
              <view class="spec-name">{{ item.name }}</view>
              <!-- <view class="spec-desc">{{ item.desc }}</view> -->
              <view class="spec-price">¥{{ item.price }}</view>
            </view>
            <view class="quantity-control">
              <view class="quantity-btn" @click="decrementQuantity(index)">-</view>
              <view class="quantity-value">{{ item.quantity || 0 }}</view>
              <view class="quantity-btn" @click="incrementQuantity(index)">+</view>
            </view>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">租户地址</view>
        <view class="store-info">
          <view class="store-details">
            <view class="store-name">{{ storeInfo.name }}</view>
            <view class="store-contact">
              <text class="phone">{{ storeInfo.phone }}</text>
            </view>
            <view class="store-address">{{ storeInfo.address }}</view>
          </view>
          <view class="store-map">
            <image :src="storeInfo.mapImageUrl" mode="aspectFill" class="map-image"></image>
          </view>
        </view>
      </view>

      <view class="section" v-if="selectedBusiness === 2">
        <view class="section-title">配送时间</view>
        <view class="delivery-container">
          <view class="date-selector">
            <view 
              v-for="(date, index) in deliveryDates" 
              :key="index"
              :class="['date-item', { active: selectedDateIndex === index }]"
              @click="selectDate(index)"
            >
              <view class="date-week">{{ date.week }}</view>
              <view class="date-day">{{ date.day }}</view>
            </view>
          </view>
          
          <view class="time-selector" v-if="selectedDateIndex !== null">
            <view class="time-title">选择时间段</view>
            <view class="time-options">
              <view 
                v-for="(time, index) in availableTimeSlots" 
                :key="index"
                :class="['time-item', { active: selectedTimeIndex === index, disabled: !time.available }]"
                @click="selectTime(index, time.available)"
              >
                <view class="time-text">{{ time.text }}</view>
                <view class="time-price" v-if="time.extraFee > 0">+¥{{ time.extraFee }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="section" v-if="selectedBusiness === 1">
        <view class="section-title">报警器租赁</view>
        <view class="alarm-item">
          <view class="alarm-info">
            <view class="alarm-title">报警器租赁</view>
            <view class="alarm-desc">根据安全规定，每4个气瓶需绑定1个报警器</view>
            <view class="alarm-price">¥{{ alarmRentalPrice }}/个</view>
          </view>
          <view class="quantity-control">
            <view class="quantity-btn" @click="decrementAlarmQuantity">-</view>
            <view class="quantity-value">{{ alarmQuantity }}</view>
            <view class="quantity-btn" @click="incrementAlarmQuantity">+</view>
          </view>
        </view>
      </view>

      <!-- 订单总览和详情 -->
      <view class="section order-section">
        <view class="section-title">订单总览</view>
        
        <!-- 订单信息卡片 -->
        <view class="order-card">
          <view class="order-card-header">
            <text class="card-title">订单信息</text>
          </view>
          <view class="order-card-body">
            <view class="info-row">
              <text class="info-label">气瓶总数</text>
              <text class="info-value">{{ totalBottles }}个</text>
            </view>
            <view class="info-row">
              <text class="info-label">配送地址</text>
              <text class="info-value">{{ storeInfo.address }}</text>
            </view>
            <view class="info-row" v-if="selectedBusiness === 2 && deliveryDates[selectedDateIndex]">
              <text class="info-label">配送时间</text>
              <text class="info-value">{{ deliveryDates[selectedDateIndex].week }} {{ availableTimeSlots[selectedTimeIndex]?.text || '' }}</text>
            </view>
            <view class="info-row" v-if="selectedBusiness === 1 && alarmQuantity > 0">
              <text class="info-label">报警器租赁</text>
              <text class="info-value">¥{{ alarmPrice }}</text>
            </view>
          </view>
        </view>
        
        <!-- 商品明细卡片 -->
        <view class="order-card">
          <view class="order-card-header">
            <text class="card-title">商品明细</text>
          </view>
          <view class="order-card-body">
            <view 
              v-for="(item, index) in (specs || [])" 
              :key="index" 
              :class="['item-row', {'inactive-item': (item.quantity || 0) === 0}]"
            >
              <view class="item-info">
                <text class="item-name">{{ item.name }}</text>
                <text class="item-unit">¥{{ item.price }}/个</text>
              </view>
              <view class="item-summary">
                <text class="item-quantity">x {{ item.quantity || 0 }}</text>
                <text class="item-total">¥{{ (item.price * (item.quantity || 0)).toFixed(2) }}</text>
              </view>
            </view>
            
            <view class="item-row alarm-row" v-if="selectedBusiness === 1 && alarmQuantity > 0">
              <view class="item-info">
                <text class="item-name">报警器租赁</text>
                <text class="item-unit">¥{{ alarmRentalPrice }}/个</text>
              </view>
              <view class="item-summary">
                <text class="item-quantity">x {{ alarmQuantity }}</text>
                <text class="item-total">¥{{ alarmPrice }}</text>
              </view>
            </view>
            <view class="item-row" v-if="selectedBusiness === 2 && availableTimeSlots[selectedTimeIndex]?.extraFee > 0">
              <view class="item-info">
                <text class="item-name">立即配送费</text>
                <text class="item-unit">配送费</text>
              </view>
              <view class="item-summary">
                <text class="item-quantity">x 1</text>
                <text class="item-total">¥{{ availableTimeSlots[selectedTimeIndex]?.extraFee }}</text>
              </view>
            </view>
            
            <view class="order-divider"></view>
            
            <view class="total-row">
              <text class="total-label">合计</text>
              <text class="total-value">¥{{ totalPrice }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务协议 -->
      <view class="section agreement-section">
        <label class="agreement-checkbox">
          <checkbox :checked="agreementChecked" @tap="toggleAgreement" color="var(--primary-color)" />
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @tap.stop="showAgreementDetails">《服务协议》</text>
            <text v-if="selectedBusiness === 2">（换气前两小时取消订单将收取误工费）</text>
            <text v-else>（租赁前一小时取消订单将收取误工费）</text>
          </text>
        </label>
      </view>
    </div>
    
    <view class="submit-bar">
      <view class="total">
        <text>合计：</text>
        <text class="price">¥{{ totalPrice }}</text>
      </view>
      <button class="submit-btn" :disabled="!canSubmit" @click="submitOrder">
        提交订单
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      business: [
        { name: "换气", value: 2 },
        { name: "租赁", value: 1 },
      ],
      selectedBusiness: 2, // 默认选中换气
      specs: [
        {
          id: 1,
          name: "5kg液化气瓶",
          price: 70,
          image: "/static/images/5kg.jpg",
          quantity: 0
        },
        {
          id: 2,
          name: "15kg液化气瓶",
          price: 130,
          image: "/static/images/15kg.jpg",
          quantity: 0
        },
        {
          id: 3,
          name: "50kg液化气瓶",
          price: 380,
          image: "/static/images/50kg.jpg",
          quantity: 0
        },
      ],
      storeInfo: {
        name: "液化气配送中心",
        phone: "************",
        address: "广州市海珠区新港东路123号",
        latitude: 23.106574,
        longitude: 113.324520,
        mapImageUrl: "/static/images/store-map.png" // 预先准备的地图图片
      },
      deliveryTimes: ["9:00-12:00", "14:00-18:00", "18:00-21:00"],
      deliveryDates: [],
      selectedDateIndex: 0,
      selectedTimeIndex: 0,
      immediateDeliveryFee: 10, // 立即配送费用
      alarmQuantity: 0,        // 报警器数量
      alarmRentalPrice: 100,   // 报警器租赁单价
      currentBottles: 3, // 当前用户已有气瓶数量，从服务端获取
      agreementChecked: false,
    };
  },

  computed: {
    // 总气瓶数量
    totalBottles() {
      return this.specs.reduce((sum, spec) => sum + (spec.quantity || 0), 0);
    },

    // 是否需要报警器
    needAlarm() {
      return (this.currentBottles + this.totalBottles) % 4 !== 0;
    },

    // 报警器价格
    alarmPrice() {
      return this.alarmQuantity * this.alarmRentalPrice;
    },

    // 气瓶总价格
    specPrice() {
      return this.specs.reduce((sum, spec) => sum + ((spec.quantity || 0) * spec.price), 0);
    },

    // 总价格
    totalPrice() {
      let total = this.specPrice;
      
      // 添加报警器租赁费用
      if (this.selectedBusiness === 1 && this.alarmQuantity > 0) {
        total += this.alarmPrice;
      }
      
      // 添加立即配送费用 - 只在换气业务时添加
      if (this.selectedBusiness === 2) {
        const selectedTimeSlot = this.availableTimeSlots[this.selectedTimeIndex];
        if (selectedTimeSlot && selectedTimeSlot.extraFee > 0) {
          total += selectedTimeSlot.extraFee;
        }
      }
      
      return total;
    },

    // 可用时间段
    availableTimeSlots() {
      if (this.selectedDateIndex === null || !this.deliveryDates[this.selectedDateIndex]) {
        return [];
      }
      
      const selectedDate = this.deliveryDates[this.selectedDateIndex];
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      today.setHours(0, 0, 0, 0);
      
      let timeSlots = [];
      
      // 只有当天才显示立即配送选项
      if (selectedDate.date.getTime() === today.getTime()) {
        timeSlots.push({
          text: '立即配送',
          available: true,
          extraFee: this.immediateDeliveryFee
        });
      }
      
      // 添加固定时间段
      this.deliveryTimes.forEach(timeRange => {
        const [startTime] = timeRange.split('-');
        const [startHour, startMinute] = startTime.split(':').map(Number);
        
        const slotDateTime = new Date(selectedDate.date);
        slotDateTime.setHours(startHour, startMinute, 0, 0);
        
        // 如果当前日期是今天，且时间段已过，则不可用
        let available = true;
        if (selectedDate.date.getTime() === today.getTime() && now > slotDateTime) {
          available = false;
        }
        
        timeSlots.push({
          text: timeRange,
          available: available,
          extraFee: 0
        });
      });
      
      return timeSlots;
    },

    // 是否可以提交订单
    canSubmit() {
      if (this.totalBottles === 0) return false;
      if (!this.agreementChecked) return false;
      
      if (this.selectedBusiness === 2) {
        // 换气业务需要选择配送时间
        return this.selectedDateIndex !== null && this.selectedTimeIndex !== null;
      } else {
        // 租赁业务不需要配送时间
        return true;
      }
    },
  },

  onLoad() {
    // 获取用户气瓶数量
    this.getCurrentBottles();
    // 初始化规格的quantity属性，确保响应式
    this.specs.forEach((spec, index) => {
      if (typeof spec.quantity === 'undefined') {
        this.$set(this.specs[index], 'quantity', 0);
      }
    });
    // 初始化配送日期
    this.initDeliveryDates();
    this.selectedDateIndex = 0; // 默认选中今天
    this.selectedTimeIndex = 0; // 默认选中第一个可用时间
    // 这里可以添加获取店铺信息的API调用
  },

  methods: {
    // 选择业务类型
    selectBusiness(value) {
      this.selectedBusiness = value;
      
      // 重置配送时间选择
      if (value === 1) {
        // 租赁模式不需要配送时间
        this.selectedDateIndex = null;
        this.selectedTimeIndex = null;
      } else {
        // 换气模式需要配送时间，默认选择第一个
        this.selectedDateIndex = 0;
        this.selectedTimeIndex = 0;
      }
    },

    // 增加数量
    incrementQuantity(index) {
      const newQuantity = (this.specs[index].quantity || 0) + 1;
      this.$set(this.specs[index], 'quantity', newQuantity);
    },

    // 减少数量
    decrementQuantity(index) {
      if (this.specs[index].quantity > 0) {
        const newQuantity = this.specs[index].quantity - 1;
        this.$set(this.specs[index], 'quantity', newQuantity);
      }
    },

    // 增加报警器数量
    incrementAlarmQuantity() {
      this.alarmQuantity++;
    },

    // 减少报警器数量
    decrementAlarmQuantity() {
      if (this.alarmQuantity > 0) {
        this.alarmQuantity--;
      }
    },

    // 获取用户当前气瓶数量
    getCurrentBottles() {
      // 实际应用中应该调用API获取
      // 这里模拟API调用
      setTimeout(() => {
        // 模拟数据，实际应从服务器获取
        this.currentBottles = 3;
      }, 500);
    },

    // 提交订单
    submitOrder() {
      if (!this.canSubmit) return;

      uni.showLoading({
        title: "提交中",
      });

      // 构建订单数据
      const orderData = {
        businessType: this.selectedBusiness,
        specs: this.specs.filter(spec => (spec.quantity || 0) > 0).map(spec => ({
          id: spec.id,
          name: spec.name,
          quantity: spec.quantity,
          price: spec.price
        })),
        storeInfo: this.storeInfo
      };
      
      // 只有换气业务才有配送时间
      if (this.selectedBusiness === 2) {
        const selectedDate = this.deliveryDates[this.selectedDateIndex];
        const selectedTimeSlot = this.availableTimeSlots[this.selectedTimeIndex];
        
        orderData.delivery = {
          date: selectedDate,
          time: selectedTimeSlot.text,
          extraFee: selectedTimeSlot.extraFee || 0
        };
      }
      
      // 只有租赁业务才有报警器
      if (this.selectedBusiness === 1 && this.alarmQuantity > 0) {
        orderData.alarmRental = {
          quantity: this.alarmQuantity,
          price: this.alarmRentalPrice
        };
      }
      
      orderData.totalAmount = this.totalPrice;

      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();

        // 模拟订单提交成功
        const orderId = "GAS" + Date.now();

        // 调用支付
        this.requestPayment(orderId, this.totalPrice);
      }, 1000);
    },

    // 初始化配送日期
    initDeliveryDates() {
      const dates = [];
      const now = new Date();
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(now);
        date.setHours(0, 0, 0, 0); // 设置为当天的开始时间
        date.setDate(date.getDate() + i);
        
        dates.push({
          date: date,
          week: i === 0 ? '今天' : weekDays[date.getDay()],
          day: `${date.getMonth() + 1}/${date.getDate()}`
        });
      }
      
      this.deliveryDates = dates;
    },

    // 选择配送日期
    selectDate(index) {
      this.selectedDateIndex = index;
      this.selectedTimeIndex = 0; // 重置时间段选择
    },

    // 选择配送时间
    selectTime(index, available) {
      if (!available) return;
      this.selectedTimeIndex = index;
    },

    // 切换协议勾选状态
    toggleAgreement() {
      this.agreementChecked = !this.agreementChecked;
    },

    // 显示协议详情
    showAgreementDetails() {
      let content = this.selectedBusiness === 2
        ? '换气业务协议：\n1. 用户提交订单后，平台将安排配送人员按照约定时间进行配送。\n2. 用户可在约定配送时间前两小时取消订单，无需支付任何费用。\n3. 如在约定配送时间前两小时内取消订单，平台将收取适当的误工费。\n4. 用户需确保配送地址准确，并在约定时间有人接收。'
        : '租赁业务协议：\n1. 用户提交订单后，平台将安排工作人员按照约定进行服务。\n2. 用户可在约定服务时间前一小时取消订单，无需支付任何费用。\n3. 如在约定服务时间前一小时内取消订单，平台将收取适当的误工费。\n4. 报警器需按照安全规定正确使用，用户需对租赁设备妥善保管。';

      uni.showModal({
        title: '服务协议',
        content: content,
        confirmText: '我已阅读',
        success: (res) => {
          if (res.confirm) {
            this.agreementChecked = true;
          }
        }
      });
    },

    // 发起支付
    requestPayment(orderId, amount) {
      // 实际应用中，应调用服务端接口获取支付参数
      uni.showModal({
        title: "模拟支付",
        content: `订单金额：¥${amount}，是否确认支付？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: "支付中",
            });

            // 模拟支付过程
            setTimeout(() => {
              uni.hideLoading();

              uni.showToast({
                title: "支付成功",
                icon: "success",
              });

              // 跳转到订单详情页
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages/order/detail?id=${orderId}`,
                });
              }, 1500);
            }, 1000);
          }
        },
      });
    },
  },
};
</script>

<style scoped>
page {
  height: 100%;
}
.container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding-right: 0;
  padding-top: 0;
}

.main {
  padding: 0 30rpx 100rpx;
  flex: 1;
  overflow: auto;
}
.section {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border-radius: 20rpx;
}

.section-title {
  line-height: 1;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 8rpx solid var(--primary-color);
}

/* 配送时间样式 */
.delivery-container {
  display: flex;
  flex-direction: column;
}

.date-selector {
  display: flex;
  overflow-x: auto;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  height: 100rpx;
  margin-right: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.date-item.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.date-week {
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.date-day {
  font-size: 28rpx;
  font-weight: bold;
}

.time-selector {
  display: flex;
  flex-direction: column;
}

.time-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.time-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
  position: relative;
}

.time-item.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.time-item.disabled {
  background-color: #f0f0f0;
  color: #ccc;
  pointer-events: none;
}

.time-text {
  font-size: 26rpx;
  font-weight: 500;
}

.time-price {
  font-size: 20rpx;
  color: #ff6b6b;
  margin-top: 4rpx;
}

.time-item.active .time-price {
  color: #fff;
}

/* 业务类型样式 */
.business-container {
  display: flex;
  margin-bottom: 20rpx;
}

.business-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  margin: 0 10rpx;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
}

.business-item.active {
  border-color: var(--primary-color);
  background-color: rgba(41, 121, 255, 0.05);
  color: var(--primary-color);
}

/* 规格样式 */
.specs-container {
  display: flex;
  flex-direction: column;
}

.spec-item {
  display: flex;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
  border: 2rpx solid transparent;
  align-items: center;
}

.spec-item.active {
  border-color: var(--primary-color);
  background-color: rgba(41, 121, 255, 0.05);
}

/* 数量选择器样式 */
.quantity-control {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border: 1px solid #ddd;
}

.quantity-value {
  width: 80rpx;
  height: 62rpx;
  line-height: 62rpx;
  text-align: center;
  font-size: 28rpx;
  background-color: #fff;
}

.spec-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #fff;
}

.spec-info {
  flex: 1;
}

.spec-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.spec-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.spec-price {
  font-size: 32rpx;
  color: #ff6700;
  font-weight: bold;
}

/* 租户信息样式 */
.store-info {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.store-details {
  margin-bottom: 20rpx;
}

.store-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.store-contact {
  margin-bottom: 10rpx;
}

.phone {
  color: #666;
  font-size: 28rpx;
}

.store-address {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.store-map {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.map-image {
  width: 100%;
  height: 100%;
}

/* 报警器租赁 */
.alarm-item {
  display: flex;
  padding: 20rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
  border: 2rpx solid transparent;
  align-items: center;
}

.alarm-info {
  flex: 1;
}

.alarm-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.alarm-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.alarm-price {
  font-size: 32rpx;
  color: #ff6700;
  font-weight: bold;
}

/* 订单详情与总览 */
.order-section {
  margin-bottom: 120rpx;
}

.order-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-card:last-child {
  margin-bottom: 0;
}

.order-card-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #f9f9f9;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.order-card-body {
  padding: 24rpx;
}

/* 订单信息部分 */
.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  align-items: center;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 500;
  max-width: 380rpx;
  text-align: right;
  word-break: break-all;
}

/* 商品明细部分 */
.item-row {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
}

.inactive-item {
  display: none;
}

.item-info {
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.item-unit {
  font-size: 24rpx;
  color: #999;
}

.item-summary {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}

.item-quantity {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.item-total {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
}

.total-label {
  color: #333;
  font-weight: bold;
}

.total-value {
  color: #ff6700;
  font-size: 36rpx;
  font-weight: bold;
}

/* 服务协议 */
.agreement-section {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.agreement-link {
  color: var(--primary-color);
}

/* 提交栏 */
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total {
  flex: 1;
  font-size: 28rpx;
}

.price {
  color: #ff6700;
  font-size: 36rpx;
  font-weight: bold;
}

.submit-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}
.submit-btn::after {
  border: none;
}
</style>
