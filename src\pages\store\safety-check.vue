<template>
  <view class="container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">安全核查详情</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容 -->
    <view v-else class="content">
      <!-- 服务商信息 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>服务商信息</text>
        </view>
        <view class="info-card">
          <view class="info-row">
            <text class="info-label">服务商名称</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.name }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.contact }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.phone }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">核查时间</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.checkTime }}</text>
          </view>
        </view>
      </view>

      <!-- 使用场景 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>使用场景</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.usageScene.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.usageScene.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.usageScene.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 气瓶存放区 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>气瓶存放区</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.cylinderStorage.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.cylinderStorage.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 报警器安装 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>报警器安装</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.alarmInstallation.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.alarmInstallation.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 消防设备 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>消防设备</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.fireEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.fireEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 管道阀门安装图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>管道阀门安装图</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.pipelineValve.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.pipelineValve.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 燃气设备使用图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>燃气设备使用图</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.gasEquipment.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.gasEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.gasEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const storeId = ref(null)
const loading = ref(true)
const safetyCheckData = ref({})

// 页面加载时的处理
const onLoad = (options) => {
  storeId.value = options.id;
  loadSafetyCheckData();
}

// 返回上一页
const goBack = () => {
  uni.navigateBack();
}

// 加载安全核查数据
const loadSafetyCheckData = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    safetyCheckData.value = {
      serviceProvider: {
        name: "广州市燃气安全检测有限公司",
        contact: "李工程师",
        phone: "020-12345678",
        checkTime: "2023-10-15 14:30"
      },
      usageScene: {
        description: "通风良好，有通风设备",
        images: [
          "https://via.placeholder.com/300x200/4a66b7/ffffff?text=通风设备1",
          "https://via.placeholder.com/300x200/2979ff/ffffff?text=通风设备2"
        ]
      },
      cylinderStorage: {
        images: [
          "https://via.placeholder.com/300x200/34c759/ffffff?text=气瓶存放1",
          "https://via.placeholder.com/300x200/ff9500/ffffff?text=气瓶存放2",
          "https://via.placeholder.com/300x200/ff3b30/ffffff?text=气瓶存放3"
        ]
      },
      alarmInstallation: {
        images: [
          "https://via.placeholder.com/300x200/909399/ffffff?text=报警器1",
          "https://via.placeholder.com/300x200/606266/ffffff?text=报警器2"
        ]
      },
      fireEquipment: {
        images: [
          "https://via.placeholder.com/300x200/e6a23c/ffffff?text=消防设备1",
          "https://via.placeholder.com/300x200/f56c6c/ffffff?text=消防设备2",
          "https://via.placeholder.com/300x200/67c23a/ffffff?text=消防设备3"
        ]
      },
      pipelineValve: {
        images: [
          "https://via.placeholder.com/300x200/409eff/ffffff?text=管道阀门1",
          "https://via.placeholder.com/300x200/303133/ffffff?text=管道阀门2"
        ]
      },
      gasEquipment: {
        description: "灶具有息火保护装置，热水器烟道是强排",
        images: [
          "https://via.placeholder.com/300x200/909399/ffffff?text=燃气灶具",
          "https://via.placeholder.com/300x200/606266/ffffff?text=热水器",
          "https://via.placeholder.com/300x200/303133/ffffff?text=烟道设备"
        ]
      }
    };

    loading.value = false;
  }, 1000);
}

// 预览图片
const previewImage = (currentImage, imageList) => {
  uni.previewImage({
    urls: imageList,
    current: currentImage
  });
}

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
}

.back-btn {
  margin-right: 20rpx;
  padding: 10rpx;
}

.back-icon {
  font-size: 36rpx;
  color: #333;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4a66b7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.section-title-bar {
  width: 6rpx;
  height: 32rpx;
  background-color: #4a66b7;
  margin-right: 16rpx;
  border-radius: 3rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 信息卡片 */
.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 220rpx;
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

/* 图片区域 */
.image-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.scene-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.image-item {
  width: 48%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 20rpx;
}

.image-item:active {
  opacity: 0.8;
}

.scene-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

/* 移除高级CSS选择器以兼容小程序 */
</style>
