<template>
  <view class="add-store">
    <!-- <page-header :title="isEdit ? '编辑租户' : '新增租户'" /> -->
    
    
    <view class="content">
      <step-indicator 
        :steps="steps" 
        :currentStep="currentStep" 
        @step-change="navigateToStep" 
      />
      <!-- Step components -->
      <basic-info-step 
        v-if="currentStep === 0" 
        v-model="storeForm"
        :typeOptions="typeOptions"
        :typeIndex="typeIndex"
        @type-change="onTypeChange"
        ref="basicInfoStep"
      />
      
      <appointment-step
        v-if="currentStep === 1"
        v-model="storeForm"
        ref="appointmentStep"
      />

      <contract-step
        v-if="currentStep === 2"
        v-model="storeForm.signature"
        @sign="openSignature"
        ref="contractStep"
      />
    </view>
                
    <!-- Navigation buttons -->
    <view class="actions">
      <!-- 第二步预约成功后只显示回到首页按钮 -->
      <step-button
        class="action-btn"
        v-if="currentStep === 1 && storeForm.appointment && storeForm.appointment.status"
        @click="goToHome"
        type="primary"
      >回到首页</step-button>

      <!-- 其他情况的按钮 -->
      <template v-else>
        <step-button class="action-btn" v-if="currentStep > 0 && currentStep !== 2" @click="prevStep" type="primary">上一步</step-button>
        <step-button
          class="action-btn"
          v-if="currentStep < steps.length - 1"
          @click="nextStep"
          type="primary"
        >{{ getNextButtonText() }}</step-button>
        <step-button
          class="action-btn"
          v-if="currentStep === steps.length - 1"
          @click="submitForm"
          type="primary"
        >提交</step-button>
      </template>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PageHeader from './components/PageHeader.vue'
import StepIndicator from './components/StepIndicator.vue'
import BasicInfoStep from './components/steps/BasicInfoStep.vue'
import ContractStep from './components/steps/ContractStep.vue'
import AppointmentStep from './components/steps/AppointmentStep.vue'
import StepButton from './components/ui/UniButton.vue'

// 响应式数据
const isEdit = ref(false)
const storeId = ref(null)
const currentStep = ref(0)
const steps = ref(['基本信息', '预约安装', '电子合同'])
const typeOptions = ref([
  { name: '居民用户', value: 'resident' },
  { name: '移动用户', value: 'mobile' },
  { name: '店铺/企业用户', value: 'business-enterprise' }
])
const typeIndex = ref(0)
const storeForm = ref({
  name: '',
  address: '',
  phone: '',
  type: '',
  image: '',
  images: [],
  signature: '',
  appointment: {
    contactName: '',
    contactPhone: '',
    date: '',
    timeSlot: '',
    remarks: '',
    status: 'pending'
  }
})

// 组件引用
const basicInfoStep = ref(null)
const appointmentStep = ref(null)
const contractStep = ref(null)

// 页面加载时的处理
const onLoad = (options) => {
  if (options.id) {
    isEdit.value = true
    storeId.value = options.id
    loadStoreData()
  }

  // 如果传入了step参数，直接跳转到指定步骤
  if (options.step) {
    currentStep.value = parseInt(options.step)
  }
}
// 加载店铺数据
const loadStoreData = () => {
  setTimeout(() => {
    const mockData = {
      name: '燃气经营部(总店)',
      address: '广州市天河区珠江新城88号',
      phone: '***********',
      type: '居民用户',
      image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
      // Initialize images array for multiple photos
      images: [
        'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3'
      ],
      signature: '',
      safetyManagers: [
        {
          name: '李四',
          phone: '13800138000',
          canUnlock: true
        }
      ],
      safetyImages: {
        fire: [],
        cylinder: [],
        alarm: []
      }
    }

    storeForm.value = mockData
    // Find index by name
    typeIndex.value = typeOptions.value.findIndex(option => option.name === mockData.type)
    if (typeIndex.value === -1) typeIndex.value = 0
  }, 500)
}

// 类型改变处理
const onTypeChange = (e) => {
  typeIndex.value = e
  storeForm.value.type = typeOptions.value[typeIndex.value].name
}
// 导航到指定步骤
const navigateToStep = (index) => {
  if (index < currentStep.value || validateCurrentStep()) {
    currentStep.value = index
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 下一步
const nextStep = () => {
  if (currentStep.value === 1) {
    // 第二步：检查是否已有预约
    const appointmentRef = appointmentStep.value
    if (appointmentRef) {
      // 如果已有预约，直接跳转到下一步
      if (storeForm.value.appointment && storeForm.value.appointment.status) {
        if (validateCurrentStep() && currentStep.value < steps.value.length - 1) {
          currentStep.value++
        }
        return
      }

      // 如果没有预约，先验证表单，再提交预约
      if (!validateCurrentStep()) {
        return // 验证失败，停止执行
      }

      if (typeof appointmentRef.submitAppointment === 'function') {
        appointmentRef.submitAppointment().then(() => {
          // 预约成功后，停留在当前步骤显示预约状态
          // 不自动跳转到下一步，让用户查看预约信息
          uni.showToast({
            title: '预约成功，请查看预约信息',
            icon: 'success',
            duration: 2000
          })
        }).catch(() => {
          // 预约失败，停留在当前步骤
        })
      }
    }
  } else {
    // 其他步骤：正常验证和跳转
    if (validateCurrentStep() && currentStep.value < steps.value.length - 1) {
      currentStep.value++
    }
  }
}

// 获取下一步按钮文本
const getNextButtonText = () => {
  // 根据当前步骤返回不同的按钮文本
  switch (currentStep.value) {
    case 0:
      return '下一步'
    case 1:
      // 第二步：根据是否已有预约显示不同文本
      if (storeForm.value.appointment && storeForm.value.appointment.status) {
        return '下一步'
      } else {
        return '确认预约'
      }
    case 2:
      return '下一步'
    default:
      return '下一步'
  }
}

// 验证当前步骤
const validateCurrentStep = () => {
  switch (currentStep.value) {
    case 0:
      // Use the validation from BasicInfoStep component
      const basicInfoRef = basicInfoStep.value
      if (basicInfoRef && typeof basicInfoRef.validateAll === 'function') {
        return basicInfoRef.validateAll()
      }

      // Fallback to old validation if component reference isn't available
      if (!storeForm.value.name) {
        uni.showToast({ title: '请输入用气户名', icon: 'none' })
        return false
      }
      if (!storeForm.value.address) {
        uni.showToast({ title: '请选择用气地址', icon: 'none' })
        return false
      }
      if (!storeForm.value.phone) {
        uni.showToast({ title: '请输入联系电话', icon: 'none' })
        return false
      }
      if (!Array.isArray(storeForm.value.images) || storeForm.value.images.length === 0) {
        uni.showToast({ title: '请上传至少一张用气场所照片', icon: 'none' })
        return false
      }
      return true

    case 1:
      // 使用预约组件的验证方法
      const appointmentRef = appointmentStep.value
      if (appointmentRef && typeof appointmentRef.validateAll === 'function') {
        return appointmentRef.validateAll()
      }

      // 如果组件引用不可用，直接返回 true，让组件内部的 submitAppointment 处理验证
      return true

    case 2:
      // 使用合同组件的验证方法
      const contractRef = contractStep.value
      if (contractRef && typeof contractRef.validateAll === 'function') {
        return contractRef.validateAll()
      }

      // 如果组件引用不可用，使用备用验证
      if (!storeForm.value.signature) {
        uni.showToast({ title: '请完成合同签名', icon: 'none' })
        return false
      }
      return true

    default:
      return true
  }
}
// 选择图片
const chooseImage = (type) => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const tempFilePaths = res.tempFilePaths

      if (type === 'store') {
        // Update both single image and images array
        console.log('Selected store image:', tempFilePaths[0]);

        // Create a new object to ensure reactivity
        const updatedForm = JSON.parse(JSON.stringify(storeForm.value));
        updatedForm.image = tempFilePaths[0];

        // Make sure images is initialized as an array
        if (!Array.isArray(updatedForm.images)) {
          updatedForm.images = [];
        }

        // Add the new image
        updatedForm.images.push(tempFilePaths[0]);
        console.log('Updated store images:', updatedForm.images);

        // Replace the entire storeForm to ensure reactivity
        storeForm.value = updatedForm;

        // Force re-render the component by recreating the object
        storeForm.value = { ...storeForm.value };
      } else if (type === 'fire' || type === 'cylinder' || type === 'alarm') {
        storeForm.value.safetyImages[type].push(tempFilePaths[0]);
      }
    }
  })
}
// 删除图片
const deleteImage = (category, index) => {
  console.log('Deleting image:', category, index);

  if (category === 'store') {
    // Handle store image deletion
    if (Array.isArray(storeForm.value.images)) {
      const updatedImages = [...storeForm.value.images];
      updatedImages.splice(index, 1);

      const updatedForm = { ...storeForm.value, images: updatedImages };

      // Update the single image reference if needed
      if (updatedImages.length > 0) {
        updatedForm.image = updatedImages[0];
      } else {
        updatedForm.image = '';
      }

      console.log('Updated store images after deletion:', updatedForm.images);
      storeForm.value = updatedForm;
    }
  } else {
    // Handle safety images deletion
    storeForm.value.safetyImages[category].splice(index, 1)
  }
}

// 打开签名
const openSignature = () => {
  uni.showLoading({ title: '签名中' })
  setTimeout(() => {
    uni.hideLoading()
    storeForm.value.signature = 'https://ts3.tc.mm.bing.net/th/id/OIP-C.HuU3CGDvCGtJzkj7NjLgsQHaCe?w=350&h=117&c=7&r=0&o=5&dpr=1.3&pid=1.7'
    uni.showToast({ title: '签名完成', icon: 'success' })
  }, 1500)
}

// 回到首页
const goToHome = () => {
  uni.reLaunch({
    url: '/pages/index/index'
  })
}

// 提交表单
const submitForm = () => {
  if (!validateCurrentStep()) return

  uni.showLoading({ title: '提交中' })

  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: isEdit.value ? '租户更新成功' : '租户创建成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          uni.navigateBack()
        }, 2000)
      }
    })
  }, 2000)
}

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
page {
  height: 100%;
  background-color: #fff;
}
.add-store {
  display: flex;
  flex-direction: column;
  padding-top: 30rpx;
  height: calc(100% - 160rpx);
  overflow: hidden;
  /* min-height: 100vh; */
}

.content {
  padding: 20rpx 30rpx;
  /* 底部留出按钮区域的空间，避免内容被遮挡 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
  flex: 1;
  overflow: auto;
}

.actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx 30rpx;
  /* 兼容安卓和iOS的安全区域 */
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom)); /* iOS 11.0-11.2 兼容 */
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  z-index: 1000;
}
.action-btn {
  flex: 1;
  width: 100%;
  border-radius: 44rpx;
  background: var(--primary-color);
}
</style> 