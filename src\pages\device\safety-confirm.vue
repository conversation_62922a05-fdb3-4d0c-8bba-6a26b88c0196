<template>
  <view class="safety-container">
    <!-- 内容区域 - 可滚动 -->
    <scroll-view scroll-y class="safety-content">
      <div class="content">
        <view class="header">
          <view class="title">确认安全</view>
          <view class="subtitle">开启燃气阀门前，请确认安全事项</view>
        </view>

        <!-- 气瓶信息展示区域 -->
        <view class="device-info-card">
          <view class="device-info-header">
            <text class="device-info-title">气瓶信息</text>
          </view>

          <view class="device-info-content">
            <view class="info-row">
              <text class="info-label">气瓶编号</text>
              <text class="info-value"
                >{{ deviceInfo.code }}
                <text v-if="deviceInfo.codeStatus" class="status-tag normal">{{
                  deviceInfo.codeStatus
                }}</text></text
              >
            </view>
            <view class="info-row">
              <text class="info-label">报警器号</text>
              <text class="info-value"
                >{{ deviceInfo.alarmCode }}
                <text
                  v-if="deviceInfo.alarmCodeStatus"
                  class="status-tag normal"
                  >{{ deviceInfo.alarmCodeStatus }}</text
                ></text
              >
            </view>
            <view class="info-row">
              <text class="info-label">充装单位</text>
              <text class="info-value">{{ deviceInfo.fillingCompany }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">充装时间</text>
              <text class="info-value">{{ deviceInfo.fillingDate }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">充装重量</text>
              <text class="info-value">{{ deviceInfo.fillingWeight }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">瓶阀电量</text>
              <view class="info-value battery-info">
                <view class="battery-bar">
                  <view
                    class="battery-fill"
                    :style="{
                      width: deviceInfo.battery + '%',
                      backgroundColor: getBatteryColor(deviceInfo.battery),
                    }"
                  ></view>
                </view>
                <text
                  class="battery-text"
                  :style="{ color: getBatteryTextColor(deviceInfo.battery) }"
                  >{{ deviceInfo.battery }}%</text
                >
              </view>
            </view>
          </view>
        </view>

        <view class="safety-card">
          <view class="safety-header">
            <text class="safety-title">燃气开锁安全事项</text>
            <view class="play-button" @click="toggleVoice">
              <text class="play-icon">{{ isPlaying ? "❙❙" : "▶" }}</text>
              <text class="play-text">{{ isPlaying ? "暂停" : "播放" }}</text>
            </view>
          </view>

          <view class="safety-items">
            <view
              class="safety-item"
              v-for="(item, index) in safetyItems"
              :key="index"
            >
              <text class="item-number">{{ index + 1 }}</text>
              <text class="item-text">{{ item }}</text>
            </view>
          </view>
        </view>

        <view class="checklist">
          <view class="checklist-item" @click="toggleCheckItem(0)">
            <view class="checkbox" :class="{ checked: checkedItems[0] }">
              <text v-if="checkedItems[0]">✓</text>
            </view>
            <text class="checklist-text">我已确认房间通风良好</text>
          </view>
          <view class="checklist-item" @click="toggleCheckItem(1)">
            <view class="checkbox" :class="{ checked: checkedItems[1] }">
              <text v-if="checkedItems[1]">✓</text>
            </view>
            <text class="checklist-text">我已确认气瓶阀门上无遮挡物</text>
          </view>
          <view class="checklist-item" @click="toggleCheckItem(2)">
            <view class="checkbox" :class="{ checked: checkedItems[2] }">
              <text v-if="checkedItems[2]">✓</text>
            </view>
            <text class="checklist-text">我已确认无燃气泄漏迹象</text>
          </view>
        </view>

        <!-- 空白占位，确保底部按钮不会覆盖内容 -->
        <view class="placeholder"></view>
      </div>
    </scroll-view>

    <!-- 底部按钮 - 固定位置 -->
    <view class="footer safe-area-bottom">
      <button class="btn cancel" @click="handleCancel">取消</button>
      <button
        class="btn confirm"
        @click="handleConfirm"
        :disabled="!allChecked"
      >
        确认开锁
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      safetyItems: [
        "确保房间通风良好，打开窗户或通风设备",
        "确保周围无明火、火源或电火花",
        "使用肥皂水检查是否有燃气泄漏",
        "如闻到燃气气味，请立即打开门窗通风",
        "不要在燃气设备附近使用手机或其他电子设备",
        "如发现异常，请立即关闭总阀门并联系专业人员",
      ],
      checkedItems: [false, false, false],
      isPlaying: false,
      audioContext: null,
      deviceId: "",
      scanCode: "",
      deviceInfo: {
        code: "GB123456789",
        codeStatus: "正常",
        alarmCode: "ABB1234567",
        alarmCodeStatus: "正常",
        fillingCompany: "xx液化石油气公司",
        fillingDate: "2025-7-20",
        fillingWeight: "12.5㎏",
        battery: 50,
      },
    };
  },

  computed: {
    allChecked() {
      return this.checkedItems.every((item) => item === true);
    },
  },

  onLoad(options) {
    if (options.deviceId) {
      this.deviceId = options.deviceId;
      // 实际应用中，这里应该根据deviceId获取设备信息
      this.loadDeviceInfo(options.deviceId);
    }

    if (options.code) {
      this.scanCode = decodeURIComponent(options.code);
    }

    // 创建音频上下文
    this.audioContext = uni.createInnerAudioContext();
    this.audioContext.src = "/static/audio/safety_instructions.mp3"; // 需要添加实际的音频文件
    this.audioContext.onEnded(() => {
      this.isPlaying = false;
    });
    this.audioContext.onError(() => {
      this.isPlaying = false;
      uni.showToast({
        title: "音频播放失败",
        icon: "none",
      });
    });
  },

  onUnload() {
    // 销毁音频上下文
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },

  methods: {
    // 获取电池颜色
    getBatteryColor(battery) {
      if (battery >= 60) return "#4CD964"; // 绿色
      if (battery >= 30) return "#FF9500"; // 橙色
      return "#FF3B30"; // 红色
    },

    // 获取电池文字颜色
    getBatteryTextColor(battery) {
      if (battery >= 60) return "#4CD964"; // 绿色
      if (battery >= 30) return "#FF9500"; // 橙色
      return "#FF3B30"; // 红色
    },

    // 加载设备信息
    loadDeviceInfo(deviceId) {
      // 实际应用中应调用API获取设备信息
      // 这里使用模拟数据
      console.log("加载设备信息:", deviceId);

      // 注意：实际项目中应该通过API请求获取设备信息
      // this.deviceInfo = {...从API获取的数据}
    },

    toggleVoice() {
      if (this.isPlaying) {
        this.audioContext.pause();
        this.isPlaying = false;
      } else {
        this.audioContext.play();
        this.isPlaying = true;
      }
    },

    toggleCheckItem(index) {
      this.$set(this.checkedItems, index, !this.checkedItems[index]);
    },

    handleCancel() {
      uni.navigateBack();
    },

    handleConfirm() {
      if (!this.allChecked) {
        uni.showToast({
          title: "请确认所有安全事项",
          icon: "none",
        });
        return;
      }

      // 显示加载中
      uni.showLoading({
        title: "开锁中",
      });

      // 模拟API调用
      setTimeout(() => {
        uni.hideLoading();

        uni.showModal({
          title: "开锁成功",
          content: "阀门已成功开启，请注意用气安全",
          showCancel: false,
          success: () => {
            uni.navigateBack();
          },
        });
      }, 1500);
    },
  },
};
</script>

<style scoped>
.safety-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.safety-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 130rpx; /* 预留底部按钮的高度 */
  padding: 30rpx 0 30rpx 30rpx;
  box-sizing: border-box;
}
.safety-content .content {
  padding-right: 30rpx;
}
/* .safety-content .uni-scroll-view {
  padding-right: 30rpx;
} */

.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 添加气瓶信息卡片样式 */
.device-info-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.device-info-header {
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eee;
}

.device-info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.device-info-content {
  margin-top: 10rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
  font-size: 28rpx;
  border-bottom: 1px solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  color: #666;
  font-size: 28rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 2rpx 10rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  margin-left: 10rpx;
}

.status-tag.normal {
  background-color: rgba(76, 217, 100, 0.1);
  color: #4cd964;
}

.status-tag.warning {
  background-color: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
}

/* 电池相关样式 */
.battery-info {
  display: flex;
  align-items: center;
}

.battery-bar {
  width: 120rpx;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.battery-fill {
  height: 100%;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.battery-text {
  font-size: 24rpx;
  font-weight: 500;
  vertical-align: middle;
}

.safety-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.safety-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.safety-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.play-button {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

.play-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.play-text {
  font-size: 24rpx;
  color: #333;
}

.safety-items {
  margin-top: 20rpx;
}

.safety-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.item-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #2979ff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.item-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.checklist {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.checklist-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.checklist-item:last-child {
  margin-bottom: 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.checkbox.checked {
  background-color: #2979ff;
  border-color: #2979ff;
}

.checklist-text {
  font-size: 28rpx;
  color: #333;
}

/* 底部占位，防止内容被底部按钮遮挡 */
.placeholder {
  height: 120rpx;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.2及以下 */
  padding-bottom:  calc(env(safe-area-inset-bottom) + 20rpx);
}

.btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  text-align: center;
  margin: 0 15rpx;
}

.btn.cancel {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.btn.confirm {
  background: linear-gradient(135deg, #2979ff, #1565c0);
  color: #fff;
}

.btn[disabled] {
  background: linear-gradient(135deg, #bdbdbd, #9e9e9e);
  opacity: 0.8;
}
</style>
